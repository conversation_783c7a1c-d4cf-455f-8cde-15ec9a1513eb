import type { ClipboardEntry } from '@/types/clipboard';

/**
 * Formats a timestamp into a human-readable relative time string
 */
export const formatRelativeTime = (timestamp: number): string => {
  const now = Date.now();
  const diffInMs = now - timestamp;
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  } else if (diffInDays < 7) {
    return `${diffInDays}d ago`;
  } else {
    return new Date(timestamp).toLocaleDateString();
  }
};

/**
 * Truncates text content to a specified length
 */
export const truncateText = (text: string, maxLength: number = 200): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

/**
 * Formats file size in bytes to human-readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

/**
 * Detects the type of clipboard content
 */
export const detectContentType = (content: string): 'text' | 'image' | 'file' => {
  // Simple heuristics for content type detection
  if (content.startsWith('data:image/') || content.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) {
    return 'image';
  }
  
  if (content.match(/^[a-zA-Z]:\\/) || content.startsWith('/') || content.match(/\.(pdf|doc|docx|txt|csv|xlsx)$/i)) {
    return 'file';
  }
  
  return 'text';
};

/**
 * Filters clipboard entries based on search query
 */
export const filterEntries = (entries: ClipboardEntry[], searchQuery: string): ClipboardEntry[] => {
  if (!searchQuery.trim()) return entries;
  
  const query = searchQuery.toLowerCase();
  return entries.filter(entry =>
    entry.content.toLowerCase().includes(query) ||
    entry.metadata?.source?.toLowerCase().includes(query) ||
    entry.metadata?.format?.toLowerCase().includes(query)
  );
};

/**
 * Sorts clipboard entries by various criteria
 */
export const sortEntries = (
  entries: ClipboardEntry[],
  sortBy: 'timestamp' | 'content' | 'type' = 'timestamp',
  order: 'asc' | 'desc' = 'desc'
): ClipboardEntry[] => {
  const sorted = [...entries].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'timestamp':
        comparison = a.timestamp - b.timestamp;
        break;
      case 'content':
        comparison = a.content.localeCompare(b.content);
        break;
      case 'type':
        comparison = a.type.localeCompare(b.type);
        break;
    }
    
    return order === 'asc' ? comparison : -comparison;
  });
  
  return sorted;
};

/**
 * Validates clipboard entry data
 */
export const validateClipboardEntry = (entry: Partial<ClipboardEntry>): string[] => {
  const errors: string[] = [];
  
  if (!entry.content || entry.content.trim().length === 0) {
    errors.push('Content is required');
  }
  
  if (!entry.type || !['text', 'image', 'file'].includes(entry.type)) {
    errors.push('Valid type is required (text, image, or file)');
  }
  
  if (entry.content && entry.content.length > 10000) {
    errors.push('Content is too long (max 10,000 characters)');
  }
  
  return errors;
};

/**
 * Generates a preview for different content types
 */
export const generatePreview = (entry: ClipboardEntry): string => {
  switch (entry.type) {
    case 'image':
      return entry.preview || 'Image content';
    case 'file':
      return `File: ${entry.metadata?.format || 'Unknown format'}`;
    default:
      return truncateText(entry.content, 100);
  }
};

/**
 * Copies text to system clipboard
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    
    // Fallback method for older browsers
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    } catch (fallbackError) {
      console.error('Fallback copy method also failed:', fallbackError);
      return false;
    }
  }
};

/**
 * Debounce function for search input
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout>;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};
