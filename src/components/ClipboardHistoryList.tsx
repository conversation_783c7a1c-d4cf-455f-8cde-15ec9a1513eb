import React, { useMemo } from 'react';
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  List,
  ListItem,
  Divider,
  Chip,
  Alert,
} from '@mui/material';
import { Search, History, Star } from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '@/store';
import { setSearchQuery, setSelectedEntry } from '@/store/slices/clipboardSlice';
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';
import ClipboardEntry from './ClipboardEntry';

interface ClipboardHistoryListProps {
  showFavoritesOnly?: boolean;
}

const ClipboardHistoryList: React.FC<ClipboardHistoryListProps> = ({
  showFavoritesOnly = false,
}) => {
  const dispatch = useAppDispatch();
  const { history, favorites, searchQuery } = useAppSelector((state) => state.clipboard);
  const { selectedEntryId } = useKeyboardShortcuts();

  const entries = showFavoritesOnly ? favorites : history;

  // Filter entries based on search query
  const filteredEntries = useMemo(() => {
    if (!searchQuery.trim()) return entries;
    
    const query = searchQuery.toLowerCase();
    return entries.filter(entry =>
      entry.content.toLowerCase().includes(query) ||
      entry.metadata?.source?.toLowerCase().includes(query)
    );
  }, [entries, searchQuery]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchQuery(event.target.value));
  };

  const handleEntryClick = (entryId: string) => {
    dispatch(setSelectedEntry(entryId));
  };

  const handleEntryCopy = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      // Could add a toast notification here
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  if (entries.length === 0) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        height="100%"
        p={4}
      >
        {showFavoritesOnly ? (
          <>
            <Star sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Favorites Yet
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center">
              Star clipboard entries to add them to your favorites for quick access.
            </Typography>
          </>
        ) : (
          <>
            <History sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Clipboard History
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center">
              Copy something to get started. Your clipboard history will appear here.
            </Typography>
          </>
        )}
      </Box>
    );
  }

  return (
    <Box height="100%" display="flex" flexDirection="column">
      {/* Search Bar */}
      <Box p={2} pb={1}>
        <TextField
          fullWidth
          size="small"
          placeholder={`Search ${showFavoritesOnly ? 'favorites' : 'clipboard history'}...`}
          value={searchQuery}
          onChange={handleSearchChange}
          data-testid="search-input"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search fontSize="small" />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'background.paper',
            },
          }}
        />
      </Box>

      {/* Results Info */}
      <Box px={2} pb={1}>
        <Box display="flex" alignItems="center" gap={1}>
          <Chip
            label={`${filteredEntries.length} ${filteredEntries.length === 1 ? 'item' : 'items'}`}
            size="small"
            variant="outlined"
          />
          {searchQuery && (
            <Typography variant="caption" color="text.secondary">
              filtered by "{searchQuery}"
            </Typography>
          )}
        </Box>
      </Box>

      {/* Entry List */}
      <Box flex={1} overflow="auto" px={2}>
        {filteredEntries.length === 0 ? (
          <Alert severity="info" sx={{ mt: 2 }}>
            No entries match your search criteria.
          </Alert>
        ) : (
          <List disablePadding>
            {filteredEntries.map((entry, index) => (
              <ListItem key={entry.id} disablePadding sx={{ mb: 1 }}>
                <Box width="100%">
                  <ClipboardEntry
                    entry={entry}
                    isSelected={entry.id === selectedEntryId}
                    onClick={() => handleEntryClick(entry.id)}
                    onCopy={() => handleEntryCopy(entry.content)}
                  />
                  {index < filteredEntries.length - 1 && (
                    <Divider sx={{ my: 1, opacity: 0.3 }} />
                  )}
                </Box>
              </ListItem>
            ))}
          </List>
        )}
      </Box>
    </Box>
  );
};

export default ClipboardHistoryList;
