import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { invoke } from '@tauri-apps/api/core';
import type { PreferencesState, KeyboardShortcuts } from '@/types/clipboard';

const defaultShortcuts: KeyboardShortcuts = {
  toggleApp: 'CommandOrControl+Shift+V',
  clearHistory: 'CommandOrControl+Shift+Delete',
  toggleFavorite: 'CommandOrControl+D',
  copySelected: 'Enter',
  deleteSelected: 'Delete',
  search: 'CommandOrControl+F',
  navigateUp: 'ArrowUp',
  navigateDown: 'ArrowDown',
};

const initialState: PreferencesState = {
  theme: 'dark',
  shortcuts: defaultShortcuts,
  autoStart: false,
  showInSystemTray: true,
  maxHistoryLength: 100,
  enableNotifications: true,
};

// Async thunks for Tauri commands
export const loadPreferences = createAsyncThunk(
  'preferences/load',
  async () => {
    const preferences = await invoke<PreferencesState>('get_preferences');
    return preferences;
  }
);

export const savePreferences = createAsyncThunk(
  'preferences/save',
  async (preferences: Partial<PreferencesState>) => {
    const updatedPreferences = await invoke<PreferencesState>('save_preferences', { preferences });
    return updatedPreferences;
  }
);

export const updateShortcuts = createAsyncThunk(
  'preferences/updateShortcuts',
  async (shortcuts: KeyboardShortcuts) => {
    await invoke('update_global_shortcuts', { shortcuts });
    return shortcuts;
  }
);

const preferencesSlice = createSlice({
  name: 'preferences',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<'dark' | 'light'>) => {
      state.theme = action.payload;
    },
    setAutoStart: (state, action: PayloadAction<boolean>) => {
      state.autoStart = action.payload;
    },
    setShowInSystemTray: (state, action: PayloadAction<boolean>) => {
      state.showInSystemTray = action.payload;
    },
    setMaxHistoryLength: (state, action: PayloadAction<number>) => {
      state.maxHistoryLength = action.payload;
    },
    setEnableNotifications: (state, action: PayloadAction<boolean>) => {
      state.enableNotifications = action.payload;
    },
    updateShortcut: (state, action: PayloadAction<{ key: keyof KeyboardShortcuts; value: string }>) => {
      state.shortcuts[action.payload.key] = action.payload.value;
    },
    resetShortcuts: (state) => {
      state.shortcuts = defaultShortcuts;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loadPreferences.fulfilled, (state, action) => {
        return { ...state, ...action.payload };
      })
      .addCase(savePreferences.fulfilled, (state, action) => {
        return { ...state, ...action.payload };
      })
      .addCase(updateShortcuts.fulfilled, (state, action) => {
        state.shortcuts = action.payload;
      });
  },
});

export const {
  setTheme,
  setAutoStart,
  setShowInSystemTray,
  setMaxHistoryLength,
  setEnableNotifications,
  updateShortcut,
  resetShortcuts,
} = preferencesSlice.actions;

export default preferencesSlice.reducer;
