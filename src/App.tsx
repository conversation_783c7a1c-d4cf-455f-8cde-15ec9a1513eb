import React, { useEffect } from 'react';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { Provider } from 'react-redux';
import { store, useAppDispatch } from '@/store';
import { getClipboardHistory, startMonitoring } from '@/store/slices/clipboardSlice';
import { loadPreferences } from '@/store/slices/preferencesSlice';
import themeWithOverrides from '@/theme';
import AppLayout from '@/components/AppLayout';

const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Initialize the app
    const initializeApp = async () => {
      try {
        // Load user preferences
        await dispatch(loadPreferences()).unwrap();

        // Load clipboard history
        await dispatch(getClipboardHistory()).unwrap();

        // Start clipboard monitoring
        await dispatch(startMonitoring()).unwrap();
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, [dispatch]);

  return <AppLayout />;
};

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider theme={themeWithOverrides}>
        <CssBaseline />
        <AppContent />
      </ThemeProvider>
    </Provider>
  );
}

export default App;
