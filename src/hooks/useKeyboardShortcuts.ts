import { useEffect } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { useAppSelector, useAppDispatch } from '@/store';
import {
  setSelectedEntry,
  deleteEntry,
  toggleFavorite,
  clearHistory,
} from '@/store/slices/clipboardSlice';

export const useKeyboardShortcuts = () => {
  const dispatch = useAppDispatch();
  const { shortcuts } = useAppSelector((state) => state.preferences);
  const { history, selectedEntryId } = useAppSelector((state) => state.clipboard);

  // Get currently selected entry index
  const selectedIndex = selectedEntryId 
    ? history.findIndex(entry => entry.id === selectedEntryId)
    : -1;

  // Navigation shortcuts
  useHotkeys(
    shortcuts.navigateUp,
    () => {
      if (history.length === 0) return;
      
      const newIndex = selectedIndex <= 0 ? history.length - 1 : selectedIndex - 1;
      dispatch(setSelectedEntry(history[newIndex].id));
    },
    { preventDefault: true }
  );

  useHotkeys(
    shortcuts.navigateDown,
    () => {
      if (history.length === 0) return;
      
      const newIndex = selectedIndex >= history.length - 1 ? 0 : selectedIndex + 1;
      dispatch(setSelectedEntry(history[newIndex].id));
    },
    { preventDefault: true }
  );

  // Copy selected entry
  useHotkeys(
    shortcuts.copySelected,
    async () => {
      if (!selectedEntryId) return;
      
      const selectedEntry = history.find(entry => entry.id === selectedEntryId);
      if (selectedEntry) {
        try {
          await navigator.clipboard.writeText(selectedEntry.content);
        } catch (error) {
          console.error('Failed to copy to clipboard:', error);
        }
      }
    },
    { preventDefault: true }
  );

  // Delete selected entry
  useHotkeys(
    shortcuts.deleteSelected,
    () => {
      if (!selectedEntryId) return;
      dispatch(deleteEntry(selectedEntryId));
    },
    { preventDefault: true }
  );

  // Toggle favorite
  useHotkeys(
    shortcuts.toggleFavorite,
    () => {
      if (!selectedEntryId) return;
      dispatch(toggleFavorite(selectedEntryId));
    },
    { preventDefault: true }
  );

  // Clear history
  useHotkeys(
    shortcuts.clearHistory,
    () => {
      dispatch(clearHistory());
    },
    { preventDefault: true }
  );

  // Search
  useHotkeys(
    shortcuts.search,
    () => {
      // Focus search input - this will be handled by the component
      const searchInput = document.querySelector('[data-testid="search-input"]') as HTMLInputElement;
      if (searchInput) {
        searchInput.focus();
      }
    },
    { preventDefault: true }
  );

  // Auto-select first item when search changes
  useEffect(() => {
    if (history.length > 0 && !selectedEntryId) {
      dispatch(setSelectedEntry(history[0].id));
    }
  }, [history, selectedEntryId, dispatch]);

  return {
    selectedIndex,
    selectedEntryId,
  };
};
