# Tauri + React + Typescript

# Paste King

A modern, cross-platform clipboard manager built with <PERSON><PERSON>, <PERSON><PERSON>, and TypeScript. Paste King helps you manage your clipboard history with a beautiful, accessible interface and powerful keyboard shortcuts.

## Features

### 🎯 Core Functionality
- **Clipboard History**: Automatically captures and stores all copied content
- **Favorites System**: Save important clipboard entries for quick access
- **Smart Search**: Find clipboard entries with intelligent filtering
- **Configurable History**: Set custom limits for clipboard history length
- **Cross-Platform**: Works seamlessly on macOS and Windows

### 🎨 Modern UI/UX
- **Dark Theme**: Beautiful dark theme optimized for extended use
- **Material Design**: Built with Material-UI v7 components
- **Responsive Layout**: Adaptive interface that works on different screen sizes
- **Accessibility**: Full keyboard navigation and screen reader support

### ⌨️ Keyboard Shortcuts
- `Cmd/Ctrl + Shift + V`: Toggle application window
- `Cmd/Ctrl + F`: Focus search bar
- `Arrow Up/Down`: Navigate through clipboard entries
- `Enter`: Co<PERSON> selected entry to clipboard
- `Cmd/Ctrl + D`: Toggle favorite status
- `Delete`: Remove selected entry
- `Cmd/Ctrl + Shift + Delete`: Clear all history

### 🔧 Technical Features
- **Redux Toolkit**: Robust state management
- **TypeScript**: Full type safety throughout the application
- **Vitest**: Comprehensive testing framework
- **System Integration**: Native clipboard monitoring
- **Persistent Storage**: Clipboard history survives app restarts

## Getting Started

### Prerequisites
- Node.js (v18 or higher)
- Rust (latest stable)
- Platform-specific dependencies:
  - **macOS**: Xcode Command Line Tools
  - **Windows**: Microsoft Visual Studio C++ Build Tools

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd paste-king
```

2. Install dependencies:
```bash
npm install
```

3. Install Rust dependencies:
```bash
cd src-tauri
cargo build
cd ..
```

### Development

Start the development server:
```bash
npm run dev
```

This will start both the Vite development server and the Tauri application.

### Building

Build the application for production:
```bash
npm run build
npm run tauri build
```

### Testing

Run the test suite:
```bash
npm test
```

Run tests with UI:
```bash
npm run test:ui
```

## Project Structure

```
paste-king/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── AppLayout.tsx
│   │   ├── ClipboardEntry.tsx
│   │   └── ClipboardHistoryList.tsx
│   ├── features/           # Feature-based modules
│   │   └── clipboard/      # Clipboard-specific components
│   ├── hooks/              # Custom React hooks
│   │   └── useKeyboardShortcuts.ts
│   ├── store/              # Redux store configuration
│   │   ├── index.ts
│   │   └── slices/
│   │       ├── clipboardSlice.ts
│   │       └── preferencesSlice.ts
│   ├── theme/              # MUI theme configuration
│   │   └── index.ts
│   ├── types/              # TypeScript type definitions
│   │   └── clipboard.ts
│   ├── utils/              # Utility functions
│   │   └── clipboard.ts
│   ├── __tests__/          # Test files
│   ├── App.tsx
│   └── main.tsx
├── src-tauri/              # Rust backend
│   ├── src/
│   │   ├── clipboard.rs    # Clipboard management logic
│   │   ├── lib.rs          # Main Tauri application
│   │   └── main.rs
│   ├── Cargo.toml
│   └── tauri.conf.json
├── package.json
├── tsconfig.json
├── vite.config.ts
└── vitest.config.ts
```

## Architecture

### Frontend (React + TypeScript)
- **React 18**: Modern React with hooks and concurrent features
- **Redux Toolkit**: Predictable state management with RTK Query
- **Material-UI v7**: Comprehensive component library with theming
- **React Hotkeys Hook**: Keyboard shortcut management
- **Vitest**: Fast unit testing with React Testing Library

### Backend (Rust + Tauri)
- **Tauri v2**: Secure, lightweight desktop application framework
- **Clipboard Monitoring**: Native system clipboard integration
- **Global Shortcuts**: System-wide keyboard shortcut registration
- **Persistent Storage**: Local data storage with Tauri Store plugin

### State Management
The application uses Redux Toolkit for state management with two main slices:

1. **Clipboard Slice**: Manages clipboard history, favorites, and search
2. **Preferences Slice**: Handles user settings and keyboard shortcuts

### Theme System
Built on Material-UI's theming system with:
- Custom dark theme with modern color palette
- Consistent typography scale
- Component overrides for enhanced visual design
- Responsive breakpoints and spacing

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines
- Write tests for new features
- Follow TypeScript best practices
- Use conventional commit messages
- Ensure accessibility compliance
- Test on both macOS and Windows

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built with [Tauri](https://tauri.app/) for cross-platform desktop development
- UI components from [Material-UI](https://mui.com/)
- State management with [Redux Toolkit](https://redux-toolkit.js.org/)
- Testing with [Vitest](https://vitest.dev/) and [React Testing Library](https://testing-library.com/)

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [Tauri](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode) + [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer)
